import {DisciplinaModel} from './disciplina.model';

export class CicloEstudoModel {
  id?: number;
  nome: string = '';
  disciplinas: CicloEstudoDisciplinaModel[] = new Array<CicloEstudoDisciplinaModel>;
}

export class CicloEstudoDisciplinaModel {
  id?: number;
  disciplina: DisciplinaModel = new DisciplinaModel();
  ordem: number = 0;
  tempoEstudoMeta: string = '';
  peso: number = 0;
  nivelConhecimento: number = 0;
}

export class CicloEstudoAtualModel {
  cicloEstudoId: number = 0;
  disciplinas: DisciplinaEstudoModel[] = [];
}

export interface DisciplinaEstudoModel {
  cicloEstudoDisciplinaId: number;
  ordem: number;
  disciplinaId: number;
  nomeDisciplina: string;
  tempoEstudoMeta: string;
  tempoEstudado: string;
  dataUltimoEstudo: string;
}

export class RegistroEstudoModel {
  dataRegistro = new Date();
  tempoEstudado: string = '';
  descricaoEstudo: string = '';
  cicloEstudoDisciplinaId: number = 0;
  gerarRevisoesEspacadas: boolean = false;
  qtdQuestoesAcertos: number = 0;
  qtdQuestoesErros: number = 0;
}

export class IndicadoresCicloEstudoModel {
  qtdCiclosConcluidos: number = 0;
  horasEstudadasTotal: string = '00h 00min';
  qtdQuestoesResolvidas: number = 0;
  qtdQuestoesAcertos: number = 0;
  qtdQuestoesErros: number = 0;
}
