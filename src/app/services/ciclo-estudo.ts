import {Injectable} from '@angular/core';
import {environment} from '../environment';
import {HttpClient} from '@angular/common/http';
import {Observable} from 'rxjs';
import {CicloEstudoModel} from '../models/ciclo.estudo.model';

@Injectable({
  providedIn: 'root'
})
export class CicloEstudo {

  private apiUrl = environment.apiUrl + '/v1/ciclo-estudo';

  constructor(private http: HttpClient) { }

  getAll(): Observable<CicloEstudo[]> {
    return this.http.get<CicloEstudo[]>(this.apiUrl);
  }

  getById(id: number): Observable<CicloEstudo> {
    return this.http.get<CicloEstudo>(`${this.apiUrl}/${id}`);
  }

  create(cicloEstudo: CicloEstudo): Observable<CicloEstudo> {
    return this.http.post<CicloEstudo>(this.apiUrl, cicloEstudo);
  }

  update(id: number, cicloEstudo: CicloEstudo): Observable<CicloEstudo> {
    return this.http.put<CicloEstudo>(`${this.apiUrl}/${id}`, cicloEstudo);
  }

  delete(id: number): Observable<void> {
    return this.http.delete<void>(`${this.apiUrl}/${id}`);
  }
}
