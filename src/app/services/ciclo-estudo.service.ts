import {Injectable} from '@angular/core';
import {environment} from '../environment';
import {HttpClient} from '@angular/common/http';
import {Observable} from 'rxjs';
import {
  CicloEstudoAtualModel,
  CicloEstudoModel,
  IndicadoresCicloEstudoModel,
  RegistroEstudoModel
} from '../models/ciclo.estudo.model';

@Injectable({
  providedIn: 'root'
})
export class CicloEstudoService {

  private apiUrl = environment.apiUrl + '/v1/ciclo-estudo';

  constructor(private http: HttpClient) { }

  getAll(): Observable<CicloEstudoModel[]> {
    return this.http.get<CicloEstudoModel[]>(this.apiUrl);
  }

  getById(id: number): Observable<CicloEstudoModel> {
    return this.http.get<CicloEstudoModel>(`${this.apiUrl}/${id}`);
  }

  create(cicloEstudo: CicloEstudoModel): Observable<CicloEstudoModel> {
    return this.http.post<CicloEstudoModel>(this.apiUrl, cicloEstudo);
  }

  update(id: number, cicloEstudo: CicloEstudoModel): Observable<CicloEstudoModel> {
    return this.http.put<CicloEstudoModel>(`${this.apiUrl}/${id}`, cicloEstudo);
  }

  delete(id: number): Observable<void> {
    return this.http.delete<void>(`${this.apiUrl}/${id}`);
  }

  getCicloEstutosAtual(): Observable<CicloEstudoAtualModel> {
    return this.http.get<CicloEstudoAtualModel>(`${this.apiUrl}/atual`);
  }

  consultarIndicadoresCiclo(): Observable<IndicadoresCicloEstudoModel> {
    return this.http.get<IndicadoresCicloEstudoModel>(`${this.apiUrl}/indicadores-ciclo`);
  }

  registrarEstudo(registroEstudo: RegistroEstudoModel): Observable<any> {
    return this.http.post<CicloEstudoModel>(`${this.apiUrl}/registrar-estudo`, registroEstudo);
  }

  desvincularRegistroEstudo(cicloEstudoDisciplinaId: number): Observable<any> {
    return this.http.put<any>(`${this.apiUrl}/desvincular-registro-estudo/${cicloEstudoDisciplinaId}`, null);
  }

  reiniciarCicloEstudo(cicloEstudoId: number): Observable<any> {
    return this.http.put<any>(`${this.apiUrl}/reiniciar-ciclo-estudo/${cicloEstudoId}`, null);
  }
}
