/* Variáveis de cores seguindo o padrão do sistema */
:host {
  --mat-primary-500: #1A237E;
  --mat-accent-500: #00BFA5;
  --mat-background-500: #ECEFF1;
  --mat-background-card: #FFFFFF;
  --mat-text-primary: #111217;
  --mat-text-secondary: #757575;
  --mat-success: #4CAF50;
  --mat-warning: #FF9800;
  --mat-error: #F44336;
}

/* Layout principal */
main {
  max-width: 1200px;
  margin: 0 auto;
  position: relative;
  z-index: 1; /* Garante que o conteúdo fique abaixo do toolbar */
}
/* Header com botão de ação */
.header-content {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  gap: 2rem;
}

.header-text {
  flex: 1;
}

.header-actions {
  flex-shrink: 0;
}

.add-button {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.75rem 1.5rem;
  border-radius: 0.5rem;
  font-weight: 500;
  transition: all 0.2s ease;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.add-button:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
}

/* Header da página */
.page-header {
  margin-bottom: 2rem;
  padding-top: 1rem; /* Adiciona espaço no topo para evitar sobreposição */
}

.text-2xl {
  font-size: 1.875rem;
  line-height: 2.25rem;
}

.font-semibold {
  font-weight: 600;
}

.text-primary {
  color: var(--mat-primary-500);
}

.text-secondary {
  color: var(--mat-text-secondary);
}

.mb-2 {
  margin-bottom: 0.5rem;
}

.mb-6 {
  margin-bottom: 1.5rem;
}

/* Cards */
.mat-card {
  background-color: var(--mat-background-card);
  border-radius: 0.75rem;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  padding: 2rem;
  border: 1px solid #e2e8f0;
  transition: box-shadow 0.3s ease, transform 0.2s ease;
}

.mat-card:hover {
  box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
}

/* Header dos cards */
.card-header {
  margin-bottom: 1.5rem;
  padding-bottom: 1rem;
  border-bottom: 1px solid #e2e8f0;
}

.text-xl {
  font-size: 1.25rem;
  line-height: 1.75rem;
}

.font-medium {
  font-weight: 500;
}

.header-icon {
  vertical-align: middle;
  margin-right: 0.5rem;
  color: var(--mat-primary-500);
}

.text-sm {
  font-size: 0.875rem;
  line-height: 1.25rem;
}

.mb-1 {
  margin-bottom: 0.25rem;
}

/* Formulário */
.disciplina-form {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

.form-content {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.full-width {
  width: 100%;
}

/* Melhorias nos campos de formulário */
.mat-mdc-form-field {
  width: 100%;
}

.mat-mdc-form-field .mat-mdc-text-field-wrapper {
  border-radius: 0.5rem;
}

.mat-mdc-form-field .mat-mdc-form-field-hint-wrapper,
.mat-mdc-form-field .mat-mdc-form-field-error-wrapper {
  padding: 0 1rem;
}

/* Ações do formulário */
.form-actions {
  display: flex;
  gap: 1rem;
  align-items: center;
  flex-wrap: wrap;
  padding-top: 1rem;
  border-top: 1px solid #e2e8f0;
}

.action-button {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.75rem 1.5rem;
  border-radius: 0.5rem;
  font-weight: 500;
  transition: all 0.2s ease;
}

.action-button:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
}

.cancel-button {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  color: var(--mat-text-secondary);
  transition: color 0.2s ease;
}

.cancel-button:hover {
  color: var(--mat-text-primary);
}

/* Loading */
.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 3rem;
  text-align: center;
}

.loading-text {
  margin-top: 1rem;
  color: var(--mat-text-secondary);
  font-size: 0.875rem;
}

/* Estado vazio */
.empty-state {
  text-align: center;
  padding: 3rem 2rem;
  color: var(--mat-text-secondary);
}

.empty-icon {
  margin-bottom: 1.5rem;
}

.empty-icon mat-icon {
  font-size: 4rem;
  width: 4rem;
  height: 4rem;
  color: #cbd5e0;
}

.empty-state h3 {
  font-size: 1.25rem;
  font-weight: 600;
  color: var(--mat-text-primary);
  margin-bottom: 0.5rem;
}

.empty-state p {
  font-size: 0.875rem;
  margin-bottom: 2rem;
  max-width: 400px;
  margin-left: auto;
  margin-right: auto;
  line-height: 1.5;
}

.empty-action-button {
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.75rem 1.5rem;
  border-radius: 0.5rem;
  font-weight: 500;
}

/* Grid de disciplinas */
.disciplinas-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(320px, 1fr));
  gap: 1.5rem;
  margin-top: 1.5rem;
}

/* Card individual da disciplina */
.disciplina-card {
  background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
  border: 1px solid #e2e8f0;
  border-radius: 0.75rem;
  padding: 1.5rem;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.disciplina-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
  border-color: var(--mat-accent-500);
}

.disciplina-card.editing {
  border-color: var(--mat-accent-500);
  background: linear-gradient(135deg, #e0f7fa 0%, #e8f5e8 100%);
  box-shadow: 0 0 0 2px rgba(0, 191, 165, 0.2);
}

/* Conteúdo do card */
.disciplina-content {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  gap: 1rem;
}

.disciplina-info {
  flex: 1;
  min-width: 0;
}

.disciplina-nome {
  font-size: 1.125rem;
  font-weight: 600;
  color: var(--mat-text-primary);
  margin: 0 0 0.5rem 0;
  line-height: 1.4;
  word-wrap: break-word;
}

.disciplina-meta {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  margin-top: 0.5rem;
}

.disciplina-id {
  font-size: 0.75rem;
  color: var(--mat-text-secondary);
  background: rgba(26, 35, 126, 0.1);
  padding: 0.25rem 0.5rem;
  border-radius: 0.25rem;
  font-weight: 500;
}

/* Ações do card */
.disciplina-actions {
  display: flex;
  gap: 0.5rem;
  flex-shrink: 0;
}

.action-btn {
  width: 2.5rem;
  height: 2.5rem;
  border-radius: 0.5rem;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  justify-content: center;
}

.action-btn:hover {
  transform: scale(1.1);
}

.edit-btn:hover {
  background-color: rgba(26, 35, 126, 0.1);
}

.delete-btn:hover {
  background-color: rgba(244, 67, 54, 0.1);
}

/* Indicador de edição */
.editing-indicator {
  position: absolute;
  top: 0;
  right: 0;
  background: var(--mat-accent-500);
  color: white;
  padding: 0.25rem 0.75rem;
  border-radius: 0 0.75rem 0 0.5rem;
  font-size: 0.75rem;
  font-weight: 500;
  display: flex;
  align-items: center;
  gap: 0.25rem;
}

.editing-indicator mat-icon {
  font-size: 1rem;
  width: 1rem;
  height: 1rem;
}

/* Responsividade */
@media (max-width: 768px) {
  main {
    padding: 1rem;
  }

  .mat-card {
    padding: 1.5rem;
  }

  .disciplinas-grid {
    grid-template-columns: 1fr;
    gap: 1rem;
  }

  .disciplina-card {
    padding: 1rem;
  }

  .form-actions {
    flex-direction: column;
    align-items: stretch;
  }

  .action-button,
  .cancel-button {
    justify-content: center;
  }
}

@media (max-width: 480px) {
  .page-header {
    text-align: left;
  }

  .text-2xl {
    font-size: 1.5rem;
  }

  .mat-card {
    padding: 1rem;
  }

  .disciplina-content {
    flex-direction: column;
    gap: 1rem;
  }

  .disciplina-actions {
    align-self: flex-end;
  }
}

/* Animações */
@keyframes slideIn {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.disciplina-card {
  animation: slideIn 0.3s ease-out;
}

/* Melhorias nos ícones de erro */
.mat-mdc-form-field-error mat-icon {
  font-size: 1rem;
  width: 1rem;
  height: 1rem;
  margin-right: 0.25rem;
  vertical-align: middle;
}

/* Flexbox utilities */
.flex {
  display: flex;
}

.flex-1 {
  flex: 1 1 0%;
}

.flex-col {
  flex-direction: column;
}

.items-center {
  align-items: center;
}

.justify-center {
  justify-content: center;
}

.p-6 {
  padding: 1.5rem;
}



/* Estilos para Snackbar */
.success-snackbar {
  background-color: var(--mat-success) !important;
  color: white !important;
}

.error-snackbar {
  background-color: var(--mat-error) !important;
  color: white !important;
}

/* Melhorias adicionais */
.mat-mdc-snack-bar-container {
  border-radius: 0.5rem !important;
}

/* Scroll suave */
html {
  scroll-behavior: smooth;
}

/* Correção adicional para scroll completo */
.container-base-telas {
  min-height: calc(100vh - 64px);
  padding-bottom: 3rem !important;
}

/* Estados de foco melhorados */
.mat-mdc-form-field.mat-focused .mat-mdc-form-field-outline {
  border-color: var(--mat-accent-500) !important;
  border-width: 2px !important;
}

/* Melhorias na acessibilidade */
.action-btn:focus,
.action-button:focus,
.cancel-button:focus {
  outline: 2px solid var(--mat-accent-500);
  outline-offset: 2px;
}

/* Estilos do Modal */
.disciplina-modal-panel {
  border-radius: 1rem !important;
  overflow: hidden;
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1.5rem 1.5rem 0 1.5rem;
  border-bottom: 1px solid #e2e8f0;
  margin-bottom: 1rem;
}

.modal-title {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  margin: 0;
  font-size: 1.25rem;
  font-weight: 600;
  color: var(--mat-primary-500);
}

.modal-icon {
  color: var(--mat-primary-500);
}

.close-button {
  color: var(--mat-text-secondary);
  transition: color 0.2s ease;
}

.close-button:hover {
  color: var(--mat-text-primary);
  background-color: rgba(0, 0, 0, 0.04);
}

.modal-content {
  padding: 0 1.5rem;
  max-height: 60vh;
  overflow-y: auto;
}

.modal-description {
  color: var(--mat-text-secondary);
  font-size: 0.875rem;
  margin-bottom: 1.5rem;
  line-height: 1.5;
}

.modal-actions {
  display: flex;
  justify-content: flex-end;
  gap: 1rem;
  padding: 1.5rem;
  border-top: 1px solid #e2e8f0;
  margin-top: 1rem;
}

.modal-actions .cancel-button {
  color: var(--mat-text-secondary);
  transition: color 0.2s ease;
}

.modal-actions .cancel-button:hover {
  color: var(--mat-text-primary);
  background-color: rgba(0, 0, 0, 0.04);
}

.modal-actions .action-button {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.75rem 1.5rem;
  border-radius: 0.5rem;
  font-weight: 500;
  transition: all 0.2s ease;
}

.modal-actions .action-button:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
}

/* Formulário dentro do modal */
.modal-content .disciplina-form {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.modal-content .form-content {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

/* Responsividade do modal */
@media (max-width: 768px) {
  .disciplina-modal-panel {
    margin: 1rem !important;
    max-width: calc(100vw - 2rem) !important;
    width: calc(100vw - 2rem) !important;
  }

  .modal-header {
    padding: 1rem 1rem 0 1rem;
  }

  .modal-content {
    padding: 0 1rem;
  }

  .modal-actions {
    padding: 1rem;
    flex-direction: column-reverse;
  }

  .modal-actions .action-button,
  .modal-actions .cancel-button {
    width: 100%;
    justify-content: center;
  }

  .header-content {
    flex-direction: column;
    align-items: center;
    text-align: center;
    gap: 1rem;
  }

  .header-actions {
    width: 100%;
  }

  .add-button {
    width: 100%;
    justify-content: center;
  }
}

@media (max-width: 480px) {
  .modal-title {
    font-size: 1.125rem;
  }

  .modal-description {
    font-size: 0.8rem;
  }
}

/* Indicador visual para campos obrigatórios */
.mat-mdc-form-field-required-marker {
  color: var(--mat-error);
}
