import {Component, OnInit, ElementRef, ViewChild, TemplateRef} from '@angular/core';
import {Form<PERSON>uilder, FormGroup, ReactiveFormsModule, Validators} from '@angular/forms';
import {MatTableModule} from '@angular/material/table';
import {MatDialog, MatDialogModule, MatDialogRef} from '@angular/material/dialog';
import {MatFormFieldModule} from '@angular/material/form-field';
import {MatInputModule} from '@angular/material/input';
import {MatButtonModule} from '@angular/material/button';
import {MatIconModule} from '@angular/material/icon';
import {MatSnackBar, MatSnackBarModule} from '@angular/material/snack-bar';
import {MatProgressSpinnerModule} from '@angular/material/progress-spinner';
import {CommonModule} from '@angular/common';
import {RouterModule} from '@angular/router';
import {DisciplinaService} from '../../services/disciplina.service';
import {DisciplinaModel} from '../../models/disciplina.model';

@Component({
  selector: 'app-disciplinas',
  standalone: true,
  imports: [
    CommonModule, RouterModule, ReactiveFormsModule,
    MatTableModule, MatDialogModule, MatFormFieldModule, MatInputModule, MatButtonModule, MatIconModule, MatSnackBarModule, MatProgressSpinnerModule
  ],
  templateUrl: './disciplinas.html',
  styleUrl: './disciplinas.css'
})
export class Disciplinas implements OnInit {
  @ViewChild('disciplinaModal') disciplinaModal!: TemplateRef<any>;
  @ViewChild('modalNomeInput') modalNomeInput!: ElementRef<HTMLInputElement>;

  displayedColumns = ['id', 'nome', 'actions'];
  disciplinas: DisciplinaModel[] = [];
  loading = false;
  form: FormGroup;
  editing: DisciplinaModel | null = null;
  dialogRef: MatDialogRef<any> | null = null;

  constructor(
    private disciplinaService: DisciplinaService,
    private fb: FormBuilder,
    private snackBar: MatSnackBar,
    private dialog: MatDialog,
  ) {
    this.form = this.fb.group({
      nome: ['', [Validators.required, Validators.maxLength(100)]],
    });
  }

  ngOnInit() {
    this.loadDisciplinas();
  }

  loadDisciplinas() {
    this.loading = true;
    this.disciplinaService.getAll().subscribe({
      next: (data) => this.disciplinas = data,
      error: () => this.snackBar.open('Erro ao carregar disciplinas', 'Fechar', { duration: 3000 }),
      complete: () => this.loading = false
    });
  }

  openModal() {
    this.editing = null;
    this.form.reset();

    this.dialogRef = this.dialog.open(this.disciplinaModal, {
      width: '500px',
      maxWidth: '90vw',
      disableClose: false,
      autoFocus: false,
      panelClass: 'disciplina-modal-panel'
    });

    // Foca no input após o modal abrir
    this.dialogRef.afterOpened().subscribe(() => {
      setTimeout(() => {
        if (this.modalNomeInput) {
          this.modalNomeInput.nativeElement.focus();
        }
      }, 100);
    });

    // Limpa a referência quando o modal fechar
    this.dialogRef.afterClosed().subscribe(() => {
      this.dialogRef = null;
      this.editing = null;
      this.form.reset();
    });
  }

  submit() {
    if (this.form.invalid) return;

    const value = this.form.value;
    this.loading = true;

    if (this.editing) {
      this.disciplinaService.update(this.editing.id!, value).subscribe({
        next: () => {
          this.snackBar.open('✅ Disciplina atualizada com sucesso!', 'Fechar', {
            duration: 4000,
            panelClass: ['success-snackbar']
          });
          this.loadDisciplinas();
          this.closeModal();
        },
        error: (error) => {
          console.error('Erro ao atualizar disciplina:', error);
          this.snackBar.open('❌ Erro ao atualizar disciplina. Tente novamente.', 'Fechar', {
            duration: 5000,
            panelClass: ['error-snackbar']
          });
          this.loading = false;
        }
      });
    } else {
      this.disciplinaService.create(value).subscribe({
        next: () => {
          this.snackBar.open('✅ Disciplina criada com sucesso!', 'Fechar', {
            duration: 4000,
            panelClass: ['success-snackbar']
          });
          this.loadDisciplinas();
          this.closeModal();
        },
        error: (error) => {
          console.error('Erro ao criar disciplina:', error);
          this.snackBar.open('❌ Erro ao criar disciplina. Tente novamente.', 'Fechar', {
            duration: 5000,
            panelClass: ['error-snackbar']
          });
          this.loading = false;
        }
      });
    }
  }

  edit(d: DisciplinaModel) {
    this.editing = d;
    this.form.patchValue(d);

    this.dialogRef = this.dialog.open(this.disciplinaModal, {
      width: '500px',
      maxWidth: '90vw',
      disableClose: false,
      autoFocus: false,
      panelClass: 'disciplina-modal-panel'
    });

    // Foca no input após o modal abrir
    this.dialogRef.afterOpened().subscribe(() => {
      setTimeout(() => {
        if (this.modalNomeInput) {
          this.modalNomeInput.nativeElement.focus();
          this.modalNomeInput.nativeElement.select();
        }
      }, 100);
    });

    // Limpa a referência quando o modal fechar
    this.dialogRef.afterClosed().subscribe(() => {
      this.dialogRef = null;
      this.editing = null;
      this.form.reset();
    });
  }

  closeModal() {
    if (this.dialogRef) {
      this.dialogRef.close();
    }
    this.editing = null;
    this.form.reset();
    this.loading = false;
  }

  cancel() {
    this.closeModal();
  }

  delete(id: number) {
    const disciplina = this.disciplinas.find(d => d.id === id);
    const nomeDisciplina = disciplina?.nome || 'esta disciplina';

    if (confirm(`Tem certeza que deseja excluir "${nomeDisciplina}"?\n\nEsta ação não pode ser desfeita.`)) {
      this.loading = true;
      this.disciplinaService.delete(id).subscribe({
        next: () => {
          this.snackBar.open('🗑️ Disciplina excluída com sucesso!', 'Fechar', {
            duration: 4000,
            panelClass: ['success-snackbar']
          });
          this.loadDisciplinas();

          // Se estava editando a disciplina excluída, cancela a edição
          if (this.editing?.id === id) {
            this.cancel();
          }
        },
        error: (error) => {
          console.error('Erro ao excluir disciplina:', error);
          this.snackBar.open('❌ Erro ao excluir disciplina. Tente novamente.', 'Fechar', {
            duration: 5000,
            panelClass: ['error-snackbar']
          });
          this.loading = false;
        }
      });
    }
  }



  // Função para trackBy no ngFor (melhora performance)
  trackByDisciplina(index: number, disciplina: DisciplinaModel): number {
    return disciplina.id || index;
  }
}
