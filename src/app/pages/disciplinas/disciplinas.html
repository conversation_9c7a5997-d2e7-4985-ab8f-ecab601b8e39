<div class="container-base-telas">
  <main class="flex flex-1 flex-col p-6">
    <!-- Header da <PERSON>a -->
    <div class="page-header mb-6">
      <div class="header-content">
        <div class="header-text">
          <h1 class="text-2xl font-semibold text-primary mb-2">Disciplinas</h1>
        </div>
        <div class="header-actions">
          <button mat-raised-button
                  color="primary"
                  (click)="openModal()"
                  class="add-button">
            <mat-icon>add</mat-icon>
            Nova Disciplina
          </button>
        </div>
      </div>
    </div>



    <!-- Loading Spinner -->
    <div *ngIf="loading" class="loading-container">
      <mat-progress-spinner mode="indeterminate" diameter="40" color="primary"></mat-progress-spinner>
      <p class="loading-text">Carregando disciplinas...</p>
    </div>

    <!-- Lista de Disciplinas -->
    <section class="mat-card" *ngIf="!loading">
      <div class="card-header">
        <h2 class="text-xl font-medium text-primary mb-1">
          <mat-icon class="header-icon">school</mat-icon>
          Suas Disciplinas
        </h2>
        <p class="text-sm text-secondary">
          {{ disciplinas.length }} disciplina{{ disciplinas.length !== 1 ? 's' : '' }} cadastrada{{ disciplinas.length !== 1 ? 's' : '' }}
        </p>
      </div>

      <!-- Estado vazio -->
      <div *ngIf="!disciplinas.length" class="empty-state">
        <div class="empty-icon">
          <mat-icon>school</mat-icon>
        </div>
        <h3>Nenhuma disciplina cadastrada</h3>
        <p>Comece adicionando suas primeiras disciplinas de estudo para organizar melhor seu aprendizado.</p>
        <button mat-raised-button color="primary" (click)="openModal()" class="empty-action-button">
          <mat-icon>add</mat-icon>
          Adicionar Primeira Disciplina
        </button>
      </div>

      <!-- Grid de Disciplinas -->
      <div *ngIf="disciplinas.length" class="disciplinas-grid">
        <div *ngFor="let disciplina of disciplinas; trackBy: trackByDisciplina"
             class="disciplina-card"
             [class.editing]="editing?.id === disciplina.id">

          <div class="disciplina-content">
            <div class="disciplina-info">
              <h3 class="disciplina-nome">{{ disciplina.nome }}</h3>
              <div class="disciplina-meta">
                <span class="disciplina-id">ID: {{ disciplina.id }}</span>
              </div>
            </div>

            <div class="disciplina-actions">
              <button mat-icon-button
                      color="primary"
                      (click)="edit(disciplina)"
                      title="Editar disciplina"
                      class="action-btn edit-btn">
                <mat-icon>edit</mat-icon>
              </button>

              <button mat-icon-button
                      color="warn"
                      (click)="delete(disciplina.id!)"
                      title="Excluir disciplina"
                      class="action-btn delete-btn">
                <mat-icon>delete</mat-icon>
              </button>
            </div>
          </div>

          <!-- Indicador de edição -->
          <div *ngIf="editing?.id === disciplina.id" class="editing-indicator">
            <mat-icon>edit</mat-icon>
            <span>Editando</span>
          </div>
        </div>
      </div>
    </section>
  </main>
</div>

<!-- Modal de Cadastro/Edição de Disciplina -->
<ng-template #disciplinaModal>
  <div class="modal-header">
    <h2 mat-dialog-title class="modal-title">
      <mat-icon class="modal-icon">{{ editing ? 'edit' : 'add' }}</mat-icon>
      {{ editing ? 'Editar Disciplina' : 'Nova Disciplina' }}
    </h2>
    <button mat-icon-button
            mat-dialog-close
            class="close-button"
            title="Fechar">
      <mat-icon>close</mat-icon>
    </button>
  </div>

  <mat-dialog-content class="modal-content">
    <p class="modal-description">
      {{ editing ? 'Atualize as informações da disciplina' : 'Adicione uma nova disciplina ao seu plano de estudos' }}
    </p>

    <form [formGroup]="form" (ngSubmit)="submit()" class="disciplina-form">
      <div class="form-content">
        <mat-form-field appearance="outline" class="full-width">
          <mat-label>Nome da Disciplina</mat-label>
          <input matInput
                 #modalNomeInput
                 formControlName="nome"
                 placeholder="Ex: Direito Constitucional, Matemática, Português..."
                 maxlength="100"
                 autocomplete="off">
          <mat-hint>Digite o nome da disciplina que deseja estudar</mat-hint>
          <mat-error *ngIf="form.get('nome')?.hasError('required')">
            <mat-icon>error</mat-icon>
            Nome da disciplina é obrigatório
          </mat-error>
          <mat-error *ngIf="form.get('nome')?.hasError('maxlength')">
            <mat-icon>error</mat-icon>
            Nome deve ter no máximo 100 caracteres
          </mat-error>
        </mat-form-field>
      </div>
    </form>
  </mat-dialog-content>

  <mat-dialog-actions class="modal-actions">
    <button mat-button
            mat-dialog-close
            type="button"
            class="cancel-button">
      <mat-icon>close</mat-icon>
      Cancelar
    </button>

    <button mat-raised-button
            color="primary"
            (click)="submit()"
            [disabled]="form.invalid || loading"
            class="action-button">
      <mat-icon>{{ editing ? 'save' : 'add' }}</mat-icon>
      {{ editing ? 'Salvar Alterações' : 'Adicionar Disciplina' }}
    </button>
  </mat-dialog-actions>
</ng-template>
