import { Component } from '@angular/core';
import { MatDialogRef, MatDialogModule } from '@angular/material/dialog';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatInputModule } from '@angular/material/input';
import { MatCheckboxModule } from '@angular/material/checkbox';
import { FormsModule } from '@angular/forms';
import { CommonModule } from '@angular/common';
import {DisciplinaEstudoModel, RegistroEstudoModel} from '../../../models/ciclo.estudo.model';
import {CicloEstudoService} from '../../../services/ciclo-estudo.service';
import {MatSnackBar} from '@angular/material/snack-bar';

@Component({
  selector: 'app-cronometro-dialog',
  standalone: true,
  imports: [
    MatDialogModule,
    MatButtonModule,
    MatIconModule,
    MatFormFieldModule,
    MatInputModule,
    MatCheckboxModule,
    FormsModule,
    CommonModule
  ],
  template: `
    <h2 mat-dialog-title>Cronômetro de Estudo</h2>
    <mat-dialog-content class="cronometro-dialog-container">
      <h3>Disciplina: {{ disciplinaEstudo.nomeDisciplina }}</h3>
      <div class="cronometro-display">
        <span>{{ horasStr }}</span>:<span>{{ minutosStr }}</span>:<span>{{ segundosStr }}</span>
      </div>
      <div class="cronometro-buttons">
        <button mat-icon-button color="primary" (click)="start()" [disabled]="running">
          <mat-icon>play_arrow</mat-icon>
        </button>
        <button mat-icon-button color="primary" (click)="pause()" [disabled]="!running">
          <mat-icon>pause</mat-icon>
        </button>
        <button mat-icon-button color="warn" (click)="stop()" [disabled]="!started">
          <mat-icon>stop</mat-icon>
        </button>
      </div>
      <div *ngIf="showDescricao" class="descricao-section">
        <mat-form-field appearance="outline" style="width: 100%;">
          <mat-label>Descrição do estudo</mat-label>
          <textarea matInput [(ngModel)]="this.registroEstudo.descricaoEstudo" rows="3"></textarea>
        </mat-form-field>

        <h4 style="margin-top: -16px;">Questões</h4>
        <div style="display: flex; gap: 16px; margin-top: -20px;">
          <mat-form-field appearance="outline" style="width: 100%;">
            <mat-label>Número de acertos</mat-label>
            <input matInput type="number" [(ngModel)]="this.registroEstudo.qtdQuestoesAcertos" min="0">
          </mat-form-field>
          <mat-form-field appearance="outline" style="width: 100%;">
            <mat-label>Número de erros</mat-label>
            <input matInput type="number" [(ngModel)]="this.registroEstudo.qtdQuestoesErros" min="0">
          </mat-form-field>
        </div>

        <div class="checkbox-section">
          <mat-checkbox [(ngModel)]="this.registroEstudo.gerarRevisoesEspacadas">
            Gerar revisões espaçadas
          </mat-checkbox>
          <div class="checkbox-hint">
            <small>Serão criadas revisões automáticas em intervalos otimizados para melhor memorização</small>
          </div>
        </div>

        <button mat-raised-button color="primary" (click)="salvar()" [disabled]="!this.registroEstudo.descricaoEstudo">
          Salvar
        </button>
      </div>
    </mat-dialog-content>
  `,
  styles: [`
    .cronometro-dialog-container {
      display: flex;
      flex-direction: column;
      align-items: center;
      min-width: 400px;
      h3 {
        align-self: flex-start;
      }
      /*padding: 24px 8px 8px 8px;*/
    }
    .cronometro-display {
      font-size: 3.5rem;
      font-family: 'Roboto Mono', monospace;
      background: #f5f5f5;
      border-radius: 16px;
      padding: 16px 32px;
      margin: 24px 0 16px 0;
      box-shadow: 0 2px 8px rgba(0,0,0,0.08);
      letter-spacing: 2px;
      color: #1976d2;
    }
    .cronometro-buttons {
      display: flex;
      gap: 16px;
      margin-bottom: 16px;
    }
    .descricao-section {
      width: 100%;
      display: flex;
      flex-direction: column;
      gap: 8px;
    }
    .checkbox-section {
      width: 100%;
      margin: 8px 0;
      display: flex;
      flex-direction: column;
      gap: 4px;
    }
    .checkbox-hint {
      margin-left: 24px;
      color: #666;
    }
  `]
})
export class CronometroDialog {
  registroEstudo: RegistroEstudoModel = new RegistroEstudoModel();

  horas = 0;
  minutos = 0;
  segundos = 0;
  timer: any = null;
  running = false;
  started = false;
  showDescricao = false;
  disciplinaEstudo: DisciplinaEstudoModel;

  constructor(
    private dialogRef: MatDialogRef<CronometroDialog>,
    private cicloEstudoService: CicloEstudoService,
    private snackBar: MatSnackBar
  ) {
    this.disciplinaEstudo = this.dialogRef._containerInstance._config.data.disciplinaEstudo;
  }

  get horasStr() {
    return this.horas.toString().padStart(2, '0');
  }
  get minutosStr() {
    return this.minutos.toString().padStart(2, '0');
  }
  get segundosStr() {
    return this.segundos.toString().padStart(2, '0');
  }

  start() {
    if (!this.running) {
      this.running = true;
      this.started = true;
      this.timer = setInterval(() => {
        this.segundos++;
        if (this.segundos >= 60) {
          this.segundos = 0;
          this.minutos++;
          if (this.minutos >= 60) {
            this.minutos = 0;
            this.horas++;
          }
        }
      }, 1000);
    }
  }

  pause() {
    if (this.running) {
      this.running = false;
      clearInterval(this.timer);
    }
  }

  stop() {
    this.running = false;
    clearInterval(this.timer);
    this.showDescricao = true;
  }

  salvar() {
    const horasHH = this.horas < 10 ? `0${this.horas}` : this.horas;
    const minutosMM = this.minutos < 10 ? `0${this.minutos}` : this.minutos;
    const segudosSS = this.segundos < 10 ? `0${this.segundos}` : this.segundos;

    const tempoEstudo = `${horasHH}:${minutosMM}:${segudosSS}`;
    this.registroEstudo.tempoEstudado = tempoEstudo;
    this.registroEstudo.cicloEstudoDisciplinaId = this.disciplinaEstudo.cicloEstudoDisciplinaId;
    this.registroEstudo.dataRegistro = new Date();

    this.cicloEstudoService.registrarEstudo(this.registroEstudo).subscribe({
      next: () => {
        this.snackBar.open('Registro de estudo salvo com sucesso!', 'Fechar', { duration: 3000 });
        this.dialogRef.close(true);
      },
      error: (erro) => {
        alert('Erro ao salvar registro de estudo.' + erro)
      }
    });
  }
}
