import {Component, Inject} from '@angular/core';
import {CommonModule} from '@angular/common';
import {MAT_DIALOG_DATA, MatDialogModule, MatDialogRef} from '@angular/material/dialog';
import {MatButtonModule} from '@angular/material/button';
import {MatIconModule} from '@angular/material/icon';
import {MatCardModule} from '@angular/material/card';
import {MatFormFieldModule} from '@angular/material/form-field';
import {MatInputModule} from '@angular/material/input';
import {MatCheckboxModule} from '@angular/material/checkbox';
import {MatProgressBarModule} from '@angular/material/progress-bar';
import {MatSnackBar, MatSnackBarModule} from '@angular/material/snack-bar';
import {FormArray, FormBuilder, FormGroup, FormsModule, ReactiveFormsModule, Validators} from '@angular/forms';
import {DisciplinaService} from '../../../services/disciplina.service';
import {PlanejamentoService} from '../../../services/planejamento.service';
import {CicloEstudoModel} from '../../../models/ciclo.estudo.model';
import {CdkDragDrop, DragDropModule, moveItemInArray} from '@angular/cdk/drag-drop';
import {DisciplinaModel} from '../../../models/disciplina.model';
import {PlanejamentoModel} from '../../../models/planejamento-model';

@Component({
  selector: 'app-planejamento-modal',
  standalone: true,
  imports: [
    CommonModule,
    MatDialogModule,
    MatButtonModule,
    MatIconModule,
    MatCardModule,
    MatFormFieldModule,
    MatInputModule,
    MatCheckboxModule,
    MatProgressBarModule,
    MatSnackBarModule,
    ReactiveFormsModule,
    FormsModule,
    DragDropModule
  ],
  templateUrl: './planejamento-modal.component.html',
  styleUrl: './planejamento-modal.component.css'
})
export class PlanejamentoModalComponent {
  currentStep = 1;
  step1Form!: FormGroup;
  step2Form!: FormGroup;
  revisaoForm!: FormGroup;
  novaDisciplinaForm!: FormGroup;

  step1Completed = false;
  step2Completed = false;
  step3Completed = false;

  planejamentoModel: PlanejamentoModel = new PlanejamentoModel();
  disciplinas: DisciplinaModel[] = [];
  disciplinasSelecionadas: any[] = [];

  loadingSugestao = false;
  erroSugestao = '';
  showNovaDisciplinaForm = false;

  intervalosPadrao = [1, 3, 7, 15, 30];

  constructor(
    public dialogRef: MatDialogRef<PlanejamentoModalComponent>,
    @Inject(MAT_DIALOG_DATA) public data: any,
    private fb: FormBuilder,
    private disciplinaService: DisciplinaService,
    private planejamentoService: PlanejamentoService,
    private snackBar: MatSnackBar
  ) {
    this.initializeForms();
    this.loadDisciplinas();
  }

  private initializeForms() {
    this.step1Form = this.fb.group({
      nome: ['', Validators.required],
      horasSemanaDisponiveis: [null, [Validators.required]],
      minutosDuracaoMaximaPorSessao: [null, [Validators.required]]
    });

    this.step2Form = this.fb.group({
      disciplinas: this.fb.array([])
    });

    this.revisaoForm = this.fb.group({
      intervalos: [this.intervalosPadrao.join(','), [Validators.required]]
    });

    this.novaDisciplinaForm = this.fb.group({
      nome: ['', Validators.required]
    });
  }

  get disciplinasFormArray(): FormArray {
    return this.step2Form.get('disciplinas') as FormArray;
  }

  get intervalosArray(): number[] {
    const intervalosStr = this.revisaoForm.get('intervalos')?.value || '';
    return intervalosStr.split(',').map((i: string) => parseInt(i.trim())).filter((i: number) => !isNaN(i));
  }

  get isNextButtonDisabled(): boolean {
    if (this.currentStep === 2) {
      const selecionadas = this.disciplinasFormArray.controls.filter(ctrl => ctrl.value.selecionada);
      return selecionadas.length === 0;
    }
    return false;
  }

  private loadDisciplinas() {
    this.disciplinaService.getAll().subscribe({
      next: (disciplinas) => {
        this.disciplinas = disciplinas;
        this.updateDisciplinasFormArray();
      },
      error: (error) => {
        console.error('Erro ao carregar disciplinas:', error);
        this.snackBar.open('Erro ao carregar disciplinas.', 'Fechar', { duration: 3000 });
      }
    });
  }

  private updateDisciplinasFormArray() {
    const formArray = this.disciplinasFormArray;
    formArray.clear();

    if (this.disciplinas && this.disciplinas.length > 0) {
      this.disciplinas.forEach((d) => {
        const controlGroup = this.fb.group({
          selecionada: [false],
          id: [d.id],
          nome: [d.nome],
          peso: [3],
          nivelConhecimento: [3]
        });
        formArray.push(controlGroup);
      });
    }
  }

  nextStep() {
    if (this.currentStep === 1 && this.step1Form.valid) {
      this.step1Completed = true;
      this.currentStep = 2;
    } else if (this.currentStep === 2) {
      const selecionadas = this.disciplinasFormArray.controls.filter(ctrl => ctrl.value.selecionada);
      if (selecionadas.length > 0) {
        this.disciplinasSelecionadas = selecionadas.map(ctrl => ctrl.value);
        this.step2Completed = true;
        this.currentStep = 3;
        this.gerarCicloEstudo();
      } else {
        this.step2Form.markAllAsTouched();
      }
    } else if (this.currentStep === 3 && this.planejamentoModel.cicloEstudo) {
      this.currentStep = 4;
    }
  }

  previousStep() {
    if (this.currentStep > 1) {
      this.currentStep--;
    }
  }

  tryNavigateToStep(step: number) {
    if (step === 1) {
      this.currentStep = 1;
    } else if (step === 2 && this.step1Completed) {
      this.currentStep = 2;
    } else if (step === 3 && this.step2Completed) {
      this.currentStep = 3;
    } else if (step === 4 && this.step3Completed) {
      this.currentStep = 4;
    }
  }

  toggleNovaDisciplinaForm() {
    this.showNovaDisciplinaForm = !this.showNovaDisciplinaForm;
    if (this.showNovaDisciplinaForm) {
      this.novaDisciplinaForm.reset();
    }
  }

  cancelarNovaDisciplina() {
    this.showNovaDisciplinaForm = false;
    this.novaDisciplinaForm.reset();
  }

  adicionarNovaDisciplina() {
    if (this.novaDisciplinaForm.valid) {
      const novaDisciplina: DisciplinaModel = {
        nome: this.novaDisciplinaForm.value.nome
      };

      this.disciplinaService.create(novaDisciplina).subscribe({
        next: (disciplinaCriada) => {
          this.disciplinas.push(disciplinaCriada);
          this.updateDisciplinasFormArray();
          this.showNovaDisciplinaForm = false;
          this.novaDisciplinaForm.reset();
          this.snackBar.open('Disciplina criada com sucesso!', 'Fechar', { duration: 3000 });
        },
        error: (error) => {
          console.error('Erro ao criar disciplina:', error);
          this.snackBar.open('Erro ao criar disciplina.', 'Fechar', { duration: 3000 });
        }
      });
    }
  }

  private gerarCicloEstudo() {
    this.loadingSugestao = true;
    this.erroSugestao = '';

    const step1 = this.step1Form.value;
    const intervalos = this.intervalosArray;

    this.planejamentoModel.nome = step1.nome;
    this.planejamentoModel.dataCriacao = new Date();
    this.planejamentoModel.horasDisponiveisPorSemana = step1.horasSemanaDisponiveis;
    this.planejamentoModel.minutosDuracaoMaximaPorSessao = step1.minutosDuracaoMaximaPorSessao;
    this.planejamentoModel.intervalosRevisao = intervalos.join(',');

    // Ciclo Estudo
    this.planejamentoModel.cicloEstudo.nome = step1.nome;
    this.planejamentoModel.cicloEstudo.disciplinas = this.disciplinasFormArray.controls
      .filter(ctrl => ctrl.value.selecionada)
      .map(ctrl => ({
        disciplina: {id: ctrl.value.id, nome: ctrl.value.nome},
        peso: ctrl.value.peso,
        ordem: ctrl.value.ordem,
        nivelConhecimento: ctrl.value.nivelConhecimento,
        tempoEstudoMeta: '00:00:00'
      }));

    this.planejamentoService.sugerirCicloEstudo(this.planejamentoModel).subscribe({
      next: (res) => {
        this.planejamentoModel = res;
        this.loadingSugestao = false;
        this.step3Completed = true;
      },
      error: (err) => {
        this.erroSugestao = 'Erro ao gerar sugestão. Tente novamente.';
        this.loadingSugestao = false;
      }
    });
  }

  getTempoTotalCiclo(): number {
    if (!this.planejamentoModel.cicloEstudo || !this.planejamentoModel.cicloEstudo.disciplinas) {
      return 0;
    }
    return this.planejamentoModel.cicloEstudo.disciplinas.reduce((total: number, d: any) => {
      const tempo = d.tempoEstudoMeta || '00:00';
      const [horas, minutos] = tempo.split(':').map((t: string) => parseInt(t));
      return total + (horas * 60) + minutos;
    }, 0);
  }

  finalizarPlanejamento() {
    if (!this.planejamentoModel.cicloEstudo) return;

    // O backend salvar o ciclo de estudo e associa ao planejamento
    this.planejamentoService.create(this.planejamentoModel).subscribe({
      next: () => {
        this.snackBar.open('Planejamento criado com sucesso!', 'Fechar', { duration: 3000 });
        this.dialogRef.close(true);
      },
      error: (error) => {
        console.error('Erro ao salvar planejamento:', error);
        this.snackBar.open('Erro ao salvar planejamento. Tente novamente.', 'Fechar', { duration: 3000 });
      }
    });
  }

  reordenarDisciplinas(event: CdkDragDrop<any[]>): void {
    if (this.planejamentoModel.cicloEstudo && this.planejamentoModel.cicloEstudo.disciplinas) {
      moveItemInArray(this.planejamentoModel.cicloEstudo.disciplinas, event.previousIndex, event.currentIndex);

      // Atualizar a ordem das disciplinas
      this.planejamentoModel.cicloEstudo.disciplinas.forEach((disciplina: any, index: number) => {
        disciplina.ordem = index + 1;
      });
    }
  }

  onCancel(): void {
    this.dialogRef.close();
  }
}
