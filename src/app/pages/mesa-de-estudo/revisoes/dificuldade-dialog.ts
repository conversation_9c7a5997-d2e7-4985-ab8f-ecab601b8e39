import { Component } from '@angular/core';
import { MatDialogRef, MatDialogModule } from '@angular/material/dialog';
import { MatButtonModule } from '@angular/material/button';
import {MatRadioButton, MatRadioGroup, MatRadioModule} from '@angular/material/radio';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';

@Component({
  selector: 'app-dificuldade-dialog',
  standalone: true,
  imports: [
    MatDialogModule,
    MatButtonModule,
    MatRadioModule,
    FormsModule,
    MatRadioGroup,
    CommonModule
  ],
  template: `
    <h2 mat-dialog-title>Como foi a dificuldade desta revisão?</h2>
    <mat-dialog-content>
      <mat-radio-group [(ngModel)]="nivelDificuldade">
        <mat-radio-button *ngFor="let nivel of niveis" [value]="nivel.valor" style="display: block; margin-bottom: 8px;">
          {{ nivel.texto }}
        </mat-radio-button>
      </mat-radio-group>
    </mat-dialog-content>
    <mat-dialog-actions align="end">
      <button mat-button (click)="onCancel()">Cancelar</button>
      <button mat-raised-button color="primary" [disabled]="!nivelDificuldade" (click)="onConfirm()">Confirmar</button>
    </mat-dialog-actions>
  `,
  styles: [`
    mat-dialog-content {
      min-width: 300px;
    }
  `]
})
export class DificuldadeDialog {
  nivelDificuldade: number | null = null;
  niveis = [
    { valor: 1, texto: 'Fácil' },
    { valor: 2, texto: 'Muito Fácil' },
    { valor: 3, texto: 'Médio' },
    { valor: 4, texto: 'Difícil' },
    { valor: 5, texto: 'Muito Difícil' }
  ];

  constructor(private dialogRef: MatDialogRef<DificuldadeDialog>) {}

  onCancel(): void {
    this.dialogRef.close();
  }

  onConfirm(): void {
    this.dialogRef.close(this.nivelDificuldade);
  }
}
