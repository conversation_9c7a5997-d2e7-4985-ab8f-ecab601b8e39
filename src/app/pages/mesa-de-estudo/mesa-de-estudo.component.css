/* Variáveis de cores do modelo */
:host {
  --mat-primary-500: #1A237E;
  --mat-accent-500: #00BFA5;
  --mat-background-500: #ECEFF1;
  --mat-background-card: #FFFFFF;
  --mat-text-primary: #111217;
  --mat-text-secondary: #757575;
}

@media (min-width: 1024px) {
  main {
    flex-direction: row;
  }
}

/* Correção para z-index e scroll */
main {
  position: relative;
  z-index: 1;
  padding-top: 1rem; /* Adiciona espaço no topo */
}

/* Cards */
.mat-card {
  background-color: var(--mat-background-card);
  border-radius: 0.5rem;
  box-shadow: 0 2px 1px -1px rgba(0,0,0,.2), 0 1px 1px 0 rgba(0,0,0,.14), 0 1px 3px 0 rgba(0,0,0,.12);
  padding: 1.5rem;
}

.mat-card-minhas-revisoes {
  width: 25rem;
}

/* Card de revisões com tamanho limitado */
aside .mat-card {
  max-height: 600px;
  min-height: 300px;
  overflow-y: auto;
  display: flex;
  flex-direction: column;
}

aside .mat-card > div:last-child {
  flex: 1;
  overflow-y: auto;
}

/* Conteúdo das abas com altura controlada */
aside .space-y-4 {
  flex: 1;
  display: flex;
  flex-direction: column;
}

aside .space-y-4 > div {
  flex: 1;
  display: flex;
  flex-direction: column;
}

/* Títulos */
.text-xl {
  font-size: 1.25rem;
  line-height: 1.75rem;
}

.font-medium {
  font-weight: 500;
}

.font-small {
  font-weight: 300;
}


.text-primary {
  color: var(--mat-primary-500);
}

.text-accent {
  color: var(--mat-accent-500);
}

.mb-4 {
  margin-bottom: 1rem;
}

.mb-1 {
  margin-bottom: 0.25rem;
}

.mb-6 {
  margin-bottom: 1.5rem;
}

/* Layout das colunas */
.flex-1 {
  flex: 1 1 0%;
}

.space-y-6 > * + * {
  margin-top: 1.5rem;
}

.space-y-2 > * + * {
  margin-top: 0.5rem;
}

.space-y-3 > * + * {
  margin-top: 0.75rem;
}

.space-y-4 > * + * {
  margin-top: 1rem;
}

.gap-6 {
  gap: 1.5rem;
}

.gap-4 {
  gap: 1rem;
}

.gap-2 {
  gap: 0.5rem;
}

.gap-1 {
  gap: 0.25rem;
}

/* Flexbox utilities */
.flex {
  display: flex;
}

.flex-col {
  flex-direction: column;
}

.flex-row {
  flex-direction: row;
}

.items-center {
  align-items: center;
}

.justify-between {
  justify-content: space-between;
}

.justify-center {
  justify-content: center;
}

.text-align-center {
  text-align: center;
}

/* Responsividade */
@media (min-width: 1024px) {
  .lg\:w-\[70\%\] {
    width: 70%;
  }

  .lg\:w-\[30\%\] {
    width: 30%;
  }

  .lg\:flex-row {
    flex-direction: row;
  }
}

@media (min-width: 768px) {
  .md\:flex-row {
    flex-direction: row;
  }
}

@media (min-width: 640px) {
  .sm\:flex-row {
    flex-direction: row;
  }
}

/* Círculo de progresso */
.progress-circle-bg {
  stroke: #E0E0E0;
  fill: transparent;
}

.progress-circle-fg {
  stroke: var(--mat-accent-500);
  stroke-linecap: round;
  transition: stroke-dashoffset 0.35s ease;
  transform: rotate(-90deg);
  transform-origin: 50% 50%;
  fill: transparent;
}

.progress-circle-text {
  fill: var(--mat-primary-500);
  font-family: "Roboto", sans-serif;
  font-size: 1.25em;
  font-weight: 500;
  text-anchor: middle;
  dominant-baseline: middle;
}

.progress-circle-subtext {
  fill: var(--mat-primary-500);
  font-family: "Roboto", sans-serif;
  font-size: 0.5em;
  font-weight: 400;
  text-anchor: middle;
  dominant-baseline: middle;
}

/* Botões */
.mat-button {
  border-radius: 0.25rem;
  padding: 0 0rem;
  height: 2.75rem;
  font-weight: 500;
  text-transform: uppercase;
  box-shadow: 0 1px 3px rgba(0,0,0,.12), 0 1px 2px rgba(0,0,0,.24);
  transition: background-color 250ms cubic-bezier(0.4, 0, 0.2, 1) 0ms,box-shadow 250ms cubic-bezier(0.4, 0, 0.2, 1) 0ms,border 250ms cubic-bezier(0.4, 0, 0.2, 1) 0ms;
  border: none;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
}

.mat-button:hover {
  filter: brightness(0.9);
}

.mat-button.mat-primary {
  background-color: var(--mat-primary-500);
  color: white;
}

.mat-button.mat-accent {
  background-color: var(--mat-accent-500);
  color: white;
}

.h-11 {
  height: 2.75rem;
}

/* Lista de disciplinas */
.mat-list-item {
  background-color: #F5F5F5;
  border-radius: 0.25rem;
  padding: 0.75rem;
  margin-bottom: 0.5rem;
}

.bg-gray-100 {
  background-color: #F3F4F6;
}

.bg-green-100 {
  background-color: #D1FAE5;
}

.text-gray-500 {
  color: #6B7280;
}

.text-green-600 {
  color: #059669;
}

.line-through {
  text-decoration: line-through;
}

.font-semibold {
  font-weight: 600;
}

.text-sm {
  font-size: 0.875rem;
  line-height: 1.25rem;
}

.text-xs {
  font-size: 0.75rem;
  line-height: 1rem;
}

.text-gray-400 {
  color: #9CA3AF;
}

/* Abas customizadas */
.mat-tab-label {
  color: var(--mat-text-secondary);
  font-weight: 500;
  padding: 0.75rem 1rem;
  border: none;
  background: none;
  cursor: pointer;
  text-decoration: none;
  transition: all 0.3s ease;
  border-bottom: 2px solid transparent;
  margin-bottom: -1px;
}

.mat-tab-label-active {
  color: var(--mat-accent-500) !important;
  border-bottom-color: var(--mat-accent-500) !important;
}

.mat-tab-label:hover {
  color: #374151;
  border-bottom-color: #D1D5DB;
}

.mat-ink-bar {
  background-color: var(--mat-accent-500);
  height: 2px;
}

.border-b {
  border-bottom-width: 1px;
}

.border-gray-200 {
  border-color: #E5E7EB;
}

.border-transparent {
  border-color: transparent;
}

.border-b-2 {
  border-bottom-width: 2px;
}

.-mb-px {
  margin-bottom: -1px;
}

.whitespace-nowrap {
  white-space: nowrap;
}

.py-3 {
  padding-top: 0.75rem;
  padding-bottom: 0.75rem;
}

.px-4 {
  padding-left: 1rem;
  padding-right: 1rem;
}

.hover\:text-gray-700:hover {
  color: #374151;
}

.hover\:border-gray-300:hover {
  border-color: #D1D5DB;
}

/* Chips */
mat-chip {
  font-size: 11px !important;
  height: 24px !important;
  border-radius: 12px !important;
}

mat-chip-set {
  display: flex;
  gap: 4px;
}

/* Form field */
mat-form-field {
  width: 100%;
}

.w-full {
  width: 100%;
}

/* Mensagens de estado vazio */
.no-revisoes, .no-ciclos {
  text-align: center;
  padding: 1.25rem;
  color: var(--mat-text-secondary);
  min-height: 120px;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  flex: 1;
}

.no-revisoes p, .no-ciclos p {
  margin-bottom: 0.5rem;
  font-size: 1rem;
}

.no-revisoes small, .no-ciclos small {
  color: #9CA3AF;
  font-size: 0.875rem;
}

.no-ciclos .mt-4 {
  margin-top: 1rem;
}

.no-ciclos button {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

/* Responsividade adicional */
@media (max-width: 768px) {
  main {
    padding: 1rem;
  }

  .mat-card {
    padding: 1rem;
  }

  .text-xl {
    font-size: 1.125rem;
  }
}

@media (max-width: 480px) {
  main {
    padding: 0.75rem;
  }

  .mat-card {
    padding: 0.75rem;
  }

  .gap-6 {
    gap: 1rem;
  }

  .space-y-6 > * + * {
    margin-top: 1rem;
  }
}

.circulo-progresso {
  max-width: 15rem;
  display: flex;
  flex-direction: column;
}

/* Indicadores do Ciclo */
.indicadores-ciclo {
  background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
  border-radius: 0.5rem;
  padding: 1.5rem;
  border: 1px solid #e2e8f0;
}

.grid {
  display: grid;
}

.grid-cols-2 {
  grid-template-columns: repeat(2, minmax(0, 1fr));
}

.gap-4 {
  gap: 1rem;
}

.indicador-item {
  text-align: center;
  padding: 1rem 0.5rem;
  background: white;
  border-radius: 0.375rem;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  transition: transform 0.2s ease, box-shadow 0.2s ease;
}

.indicador-item:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

.indicador-valor {
  font-size: 1.5rem;
  font-weight: 700;
  color: var(--mat-primary-500);
  margin-bottom: 0.25rem;
  line-height: 1;
}

.indicador-label {
  font-size: 0.75rem;
  font-weight: 500;
  color: var(--mat-text-secondary);
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

@media (min-width: 768px) {
  .md\:grid-cols-4 {
    grid-template-columns: repeat(4, minmax(0, 1fr));
  }

  .indicador-item {
    padding: 1.25rem 0.75rem;
  }

  .indicador-valor {
    font-size: 1.75rem;
  }

  .indicador-label {
    font-size: 0.875rem;
  }
}

/* Study Logger buttons */
.flex-1 {
  flex: 1 1 0%;
}

.flex {
  display: flex;
}

.items-center {
  align-items: center;
}

.justify-center {
  justify-content: center;
}

.gap-2 {
  gap: 0.5rem;
}

.gap-4 {
  gap: 1rem;
}

.h-11 {
  height: 2.75rem;
}

@media (min-width: 640px) {
  .sm\:flex-row {
    flex-direction: row;
  }
}
